#!/usr/bin/env python
# coding: utf-8

# # Classification Model 2: Decision Tree

# Input necessary Python libraries
import numpy as np
import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

# Loading the dataset 
import os
script_dir = os.path.dirname(os.path.abspath(__file__))
data_path = os.path.join(script_dir, "data", "CleandDataV20210515.csv")
currentdf  = pd.read_csv(data_path)

x = currentdf.iloc[:, 2:]
y = currentdf.iloc[:, 1]

# Splitting dataset into 75% for training and 25% for testing
X_train, X_test, y_train, y_test = train_test_split(x, y, test_size=0.25, random_state=2)

# Display the features and label from the training set 
print (X_train)
print (y_train)

# Standardize dataset 
mean = np.mean(X_train)
stddev = np.std(X_train)
X_train_sc = (X_train - mean) / stddev
X_test_sc = (X_test - mean) / stddev


# ## Building a Decision Tree Model
# 
# Let's train a model using the selected features extracted earlier and set the 'max_depth' parameter to 3.
# 

# In[23]:


from sklearn.tree import DecisionTreeClassifier
class_tree = DecisionTreeClassifier(max_depth=3) 
class_tree.fit(X_train, y_train)


# We vistualize the classification tree by importing 'export_graphviz', which exports the decision tree in a file with DOT format. This function generates a GraphiViz representation of the decision tree, which is then written into 'out_file'. Lasty, the image function is used to display the tree. 

# In[24]:


from six import StringIO
from sklearn.tree import export_graphviz
from IPython.display import Image
import pydotplus


# The code to display the graphical representation is as follows: 

# In[25]:


dot_data = StringIO()
export_graphviz(decision_tree=class_tree, 
                out_file=dot_data,
                rounded=True,
                feature_names = X_train.columns, 
                class_names = ['CottonWash', 'CottonRinse', 'CottonSpin', 'BeddingWash', 'BeddingRinse', 'BeddingSpin',
                 'DailyWash', 'DailyRinse','DailySpin'],
                special_characters=True)
graph = pydotplus.graph_from_dot_data(dot_data.getvalue())
Image(graph.create_png())


# Next, print the 'proportions' by setting the parameter for $proportion=True$. 
# 

# In[26]:


dot_data = StringIO()
export_graphviz(decision_tree=class_tree, 
                out_file=dot_data,
                rounded=True,
                proportion=True,
                feature_names = X_train.columns, 
                class_names = ['CottonWash', 'CottonRinse', 'CottonSpin', 'BeddingWash', 'BeddingRinse', 'BeddingSpin',
                 'DailyWash', 'DailyRinse','DailySpin'],
                special_characters=True)
graph = pydotplus.graph_from_dot_data(dot_data.getvalue())
Image(graph.create_png())


# Has the tree structure changed? Are you getting a different result now? Explain your observation. 

# ...
# 

# ## Training a Large Classification Tree
# 
# Let's train a larger tree by modifying the 'max_depth' to 6 and 'min_samples_split' to 50 in the scikit-learn parameters. You may also refer to https://scikit-learn.org/stable/modules/generated/sklearn.tree.DecisionTreeClassifier.html for other types of parameters.

# In[27]:


class_tree = DecisionTreeClassifier(max_depth=6, min_samples_split=50)
class_tree.fit(X_train, y_train)
y_pred_class_tree = class_tree.predict(X_train)


# We can calculate the training accuracy score of this model as follows: 

# In[28]:


from sklearn.metrics import accuracy_score 
accuracy_class_tree = accuracy_score(y_true=y_train, y_pred=y_pred_class_tree)
accuracy_class_tree


# We get 0.89133 or 89.1% accuracy for this $Decison Tree$ model by using the training data, which is much higher than the KNN model (81.4%). 
# 

# Another nice feature of this model is that we can get a normalizd score of "important" features by using the 'feature_importances_' method.

# In[29]:


pd.Series(data=class_tree.feature_importances_, index=X_train.columns).sort_values(ascending=False).round(3)


# You can also compare these values using a bar graph.

# In[30]:


pd.Series(data=class_tree.feature_importances_, index=X_train.columns).sort_values(ascending=False).plot(kind='bar');


# Discuss with your team what can you tell from these feature importance? 

# Your response here ...
# 
# 

# ## Model Evaluation
# 
# ### Confusion Matrix
# 
# Let's compute the confusion matrix for this Decision Tree model for the laudromat use case.
# 

# In[34]:


from sklearn.metrics import confusion_matrix

# Determine the accuracy of the model using test data
score = class_tree.score(X_test_sc, y_test)

# Provide the necessary label 
class_label=['CottonWash', 'CottonRinse', 'CottonSpin', 'BeddingWash', 'BeddingRinse', 'BeddingSpin',
                'DailyWash', 'DailyRinse','DailySpin']

class_tree.fit(X_train_sc, y_train)
cm = confusion_matrix(y_test, class_tree.predict(X_test_sc), labels=class_label)

axes = sns.heatmap(cm, square=True, annot=True, fmt ='d', cbar=True, cmap=plt.cm.GnBu)
axes.set_ylabel('Actual')
axes.set_ylabel('Predication')
tick_marks = np.arange(len(class_label)) + 0.5
axes.set_xticks(tick_marks)
axes.set_xticklabels(class_label, rotation = 90)
axes.set_yticks(tick_marks)
axes.set_yticklabels(class_label, rotation = 0)
axes.set_title('Confusion Matrix')
plt.show()


# Let's calculate the accuracy, precision, recall and F1 Score for our Decision Tree model using the test dataset.
# 

# In[35]:


from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

class_tree = DecisionTreeClassifier(max_depth=6, min_samples_split=50) 
class_tree.fit(X_train, y_train)

# Get prediction using test data
y_pred_test = class_tree.predict(X_test)

# Calculate the relevant metrics
accuracy = accuracy_score(y_test, y_pred_test)
precision = precision_score(y_test, y_pred_test, average='macro')
recall = recall_score(y_test, y_pred_test, average='macro')
f1 = f1_score(y_test, y_pred_test, average='macro')

print ("Accuracy: {:0.1f}%, Precision: {:0.1f}%, Recall: {:0.1f}%, F1 Score: {:0.1f}%".format(100*accuracy, 100* precision, 100*recall, 100*f1))


# Are the accuracy, precision, recall and F1 Score for Decision Tree model better or worst then your KNN model? Explain. 
# 

# Your response here ...
# 

# Lastly, you may save your Decision Tree model for future comparison. 
# 

# In[36]:


import pickle as pk

model_filename= ROOT_DIR + "/model/dt.mdl"
with open(model_filename, "wb") as file:
    pk.dump(class_tree, file)
print("Model Saved") 


# ***
