
# # 分类模型1: K近邻算法(KNN)

# 导入必要的Python库
import numpy as np
import pandas as pd
import pickle as pk
import seaborn as sns
import matplotlib.pyplot as plt

# ### 数据准备
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

# 加载数据集 
import os
script_dir = os.path.dirname(os.path.abspath(__file__))
data_path = os.path.join(script_dir, "data", "CleandDataV20210515.csv")
currentdf  = pd.read_csv(data_path)

x = currentdf.iloc[:, 2:]  # 获取特征列(第3列及以后)
y = currentdf.iloc[:, 1]   # 获取标签列(第2列)

# 显示数据集 
#print (x)

# 将数据集分割为75%训练集和25%测试集
X_train, X_test, y_train, y_test = train_test_split(x, y, test_size=0.25, random_state=2)

# 显示训练集的特征和标签
print (X_train)
print (y_train)

# 数据集标准化处理 
mean = np.mean(X_train)      # 计算训练集均值
stddev = np.std(X_train)    # 计算训练集标准差
X_train_sc = (X_train - mean) / stddev  # 标准化训练集
X_test_sc = (X_test - mean) / stddev    # 使用训练集参数标准化测试集

# ## 构建KNN模型
# 使用之前提取的特征训练模型

from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score

train_scores, test_scores = list(), list()  # 初始化训练和测试得分列表

# 定义要评估的K值范围 
values = [i for i in range(0, 10)]  # 0-9的索引
mdepth=[3, 5, 7, 9, 10, 11, 12, 13, 15, 17]  # 具体的K值列表

# 遍历不同的K值训练模型
for i in values:
    knn = KNeighborsClassifier(n_neighbors=mdepth[i])  # 创建KNN分类器
    knn.fit(X_train_sc, y_train)  # 训练模型
    
    # 在训练集上预测
    y_pred_train = knn.predict(X_train_sc)   
    train_acc = accuracy_score(y_train, y_pred_train)  # 计算训练准确率
    train_scores.append(train_acc)  # 保存训练得分
    
    # 在测试集上预测  
    y_pred_test = knn.predict(X_test_sc)    
    test_acc = accuracy_score(y_test, y_pred_test)  # 计算测试准确率
    test_scores.append(test_acc)  # 保存测试得分
    
# 模型已使用X_train中的所有特征进行训练

# 训练完第一个预测模型后，我们计算第一个性能指标-准确率来评估KNN模型。
# 准确率定义为预测正确的比例(或百分比)。让我们绘制并比较10次迭代中训练和测试数据集的准确率。

# 绘制训练和测试得分随K值变化的曲线
plt.plot(values, train_scores, '-o', label='训练集')
plt.plot(values, test_scores, '-o', label='测试集')
plt.legend()  # 显示图例

# 在此处插入代码以显示图形...
plt.show()

# 训练集的预测准确率在0.87到0.91之间(即87%-91%)。您认为这个准确率可以接受吗?

# 在此输入您的回答...

# From the plot above, build a new KNN model from the K value that produced 
# the best test accuracy result ...

train_scores, test_scores = list(), list()

knn = KNeighborsClassifier(n_neighbors=3) # Using k=3 as it produced the best result 
knn.fit(X_train_sc, y_train)
    
# Predicting on the train dataset
y_pred_train = knn.predict(X_train_sc)   
train_acc = accuracy_score(y_train, y_pred_train)
    
# Predicting on the test dataset
y_pred_test = knn.predict(X_test_sc)    
test_acc = accuracy_score(y_test, y_pred_test)

print("Training Accuracy: " + str(train_acc))
print("Test Accuracy: " + str(test_acc))

# ## 模型评估

# ### 混淆矩阵

# 混淆矩阵是一个表格，展示了分类问题中4种可能的预测结果。模型正确预测有两种情况:
# - 真正例(TP): 模型预测为正类且实际为正类。在本问题中，模型预测洗衣机模式正确。
# - 真负例(TN): 模型预测为负类且实际为负类。在本问题中，模型正确识别非当前模式。

# 模型预测错误也有两种情况:
# - 假正例(FP): 模型预测为正类但实际为负类。在本问题中，模型错误预测洗衣机模式。
# - 假负例(FN): 模型预测为负类但实际为正类。在本问题中，模型未能识别当前模式。
# ![ConfusionMatrix.png](attachment:ConfusionMatrix.png)

# 下面为洗衣店用例计算KNN模型的混淆矩阵。
from sklearn.metrics import confusion_matrix

# 计算模型准确率
score = knn.score(X_test_sc, y_test)

# 提供必要的类别标签
class_label=['CottonWash', 'CottonRinse', 'CottonSpin', 'BeddingWash', 'BeddingRinse', 'BeddingSpin',
                 'DailyWash', 'DailyRinse','DailySpin']

knn.predict(X_test_sc)
cm = confusion_matrix(y_test, knn.predict(X_test_sc), labels=class_label)
#
# 绘制混淆矩阵热力图
axes = sns.heatmap(cm, square=True, annot=True, fmt ='d', cbar=True, cmap=plt.cm.GnBu)
axes.set_ylabel('实际值')  # 设置y轴标签
axes.set_ylabel('预测值')  # 设置x轴标签(重复设置)
tick_marks = np.arange(len(class_label)) + 0.5  # 计算刻度位置
axes.set_xticks(tick_marks)  # 设置x轴刻度
axes.set_xticklabels(class_label, rotation = 90)  # 设置x轴标签并旋转90度
axes.set_yticks(tick_marks)  # 设置y轴刻度
axes.set_yticklabels(class_label, rotation = 0)  # 设置y轴标签
axes.set_title('混淆矩阵')  # 设置标题
plt.show()  # 显示图形

# 您能从混淆矩阵中得出什么结论?

# There are some metrics that we can calculate to make more sense of these numbers above. 
# The following are some of the most important metrics we can get from the quantities 
# observed in the confusion matrix: 

# - Accuracy: Proportion of cases correctly identified by the classifier 
# (N represents the total number of observation in the testing dataset):
# ![accuracy.png](attachment:accuracy.png)
# - 精确率(Precision): 正确阳性预测的比例。在本问题中，这是模型预测洗衣机模式正确的比例:
# ![precision.png](attachment:precision.png)
# - 召回率(Recall/Sensitivity): 实际阳性中被正确预测为阳性的比例。在本问题中，
# 这是模型能正确识别的实际模式比例: 
# ![recall.png](attachment:recall.png)
# - F1分数(F1 Score/Balanced F-score): F1分数可以看作是精确率和召回率的调和平均数，
# F1分数的最佳值为1，最差值为0。精确率和召回率对F1分数的贡献相等。F1分数的公式为:
# ![f1_score.png](attachment:f1_score.png)

# These 4 metrics will help us understand how good is your model. 
# So, let's calculate the accuracy, precision, recall and F1 Score 
# for our KNN model using the test dataset.

from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

knn = KNeighborsClassifier(n_neighbors=6) # 使用k=6作为超参数
knn.fit(X_train_sc, y_train)  # 训练模型

# 计算各项性能指标
accuracy = accuracy_score(y_test, y_pred_test)  # 计算准确率
precision = precision_score(y_test, y_pred_test, average='macro')  # 计算宏平均精确率
recall = recall_score(y_test, y_pred_test, average='macro')  # 计算宏平均召回率
f1 = f1_score(y_test, y_pred_test, average='macro')  # 计算宏平均F1分数

# 打印性能指标结果
print ("准确率: {:0.1f}%, 精确率: {:0.1f}%, 召回率: {:0.1f}%, F1分数: {:0.1f}%".format(100*accuracy, 100* precision, 100*recall, 100*f1))

# KNN模型对洗衣机模式做出阳性预测时，约有86%的时间是正确的(精确率)。
# 这个结果还不错。此外，模型还能正确识别84%的实际洗衣机模式。

# 为什么要使用测试数据集来计算性能指标?

# 在继续构建下一个预测模型之前，您可以保存当前的KNN模型以供将来比较。

import pickle as pk

model_filename = os.path.join(script_dir, "model", "knn.mdl")  # 模型保存路径
with open(model_filename, "wb") as file:
    pk.dump(knn, file)  # 将模型序列化保存
print("模型已保存")

