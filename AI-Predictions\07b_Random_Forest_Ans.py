#!/usr/bin/env python
# coding: utf-8

# # Classificatiom Model 3: Random Forest

# Let's develop our random forest model for the laundromat use case. 

# Input necessary Python libraries
import numpy as np
import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt

from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

# Loading the dataset 

import os
script_dir = os.path.dirname(os.path.abspath(__file__))
data_path = os.path.join(script_dir, "data", "CleandDataV20210515.csv")
currentdf  = pd.read_csv(data_path)

x = currentdf.iloc[:, 2:]
y = currentdf.iloc[:, 1]

# Display the dataset 
#print(x)

# Splitting dataset into 75% for training and 25% for testing
X_train, X_test, y_train, y_test = train_test_split(x, y, test_size=0.25, random_state=2)

# Display the features and label from the training set 
print (X_train)
print (y_train)

# Standardize dataset 
mean = np.mean(X_train)
stddev = np.std(X_train)
X_train_sc = (X_train - mean) / stddev
X_test_sc = (X_test - mean) / stddev


# Scikit-learn library usually give a good default parameters for you to work on. You may refer to https://scikit-learn.org/stable/modules/generated/sklearn.ensemble.RandomForestClassifier.html for the different types of parameters available for random forest classifier. 
# 

# In[12]:


from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score 

# define the tree depths to evaluate
values = [i for i in range(0, 10)]
mdepth = [1, 3, 4, 5, 6, 7, 8, 9, 10, 11]
train_scores, test_scores = list(), list()

for i in values:
    # Create an instance of the predictor
    rf = RandomForestClassifier(n_estimators=99,
                            max_depth=mdepth[i],
                            random_state=2)
     
    # Use the training data to train the predictor
    rf.fit(X_train_sc, y_train)
    
    # Predicting on the train dataset
    y_pred_train = rf.predict(X_train_sc)   
    train_acc = accuracy_score(y_train, y_pred_train)
    train_scores.append(train_acc)
    
    # Predicting on the test dataset
    y_pred_test = rf.predict(X_test_sc)    
    test_acc = accuracy_score(y_test, y_pred_test)
    test_scores.append(test_acc)
    


# In[13]:


# plot of train and test scores vs tree depth
plt.plot(values, train_scores, '-o', label='Train')
plt.plot(values, test_scores, '-o', label='Test')
plt.legend()
plt.show()


# We get approximately 0.85 or 85% for the test set, which is not too far from what we got from the other models.

# ## Model Evaluation
# 
# ### Confusion Matrix
# 
# Let's compute the confusion matrix for this Decision Tree model for the laudromat use case.
# 

# In[16]:


from sklearn.metrics import confusion_matrix

# Determine the accuracy of the model
score = rf.score(X_test_sc, y_test)
class_label=['CottonWash', 'CottonRinse', 'CottonSpin', 'BeddingWash', 'BeddingRinse', 'BeddingSpin',
                 'DailyWash', 'DailyRinse','DailySpin']

rf.fit(X_train_sc, y_train)
rf.predict(X_test_sc)
cm = confusion_matrix(y_test, rf.predict(X_test_sc), labels=class_label)
#
axes = sns.heatmap(cm, square=True, annot=True, fmt ='d', cbar=True, cmap=plt.cm.GnBu)
axes.set_ylabel('Actual')
axes.set_ylabel('Predication')
tick_marks = np.arange(len(class_label)) + 0.5
axes.set_xticks(tick_marks)
axes.set_xticklabels(class_label, rotation = 90)
axes.set_yticks(tick_marks)
axes.set_yticklabels(class_label, rotation = 0)
axes.set_title('Confusion Matrix')
plt.show()

print("Model Score {}" .format( score) )


# Finally, let's calculate the Accuracy, Precision, Recall and F1 Score metrics for the Random Forest model.
# 

# In[17]:


from sklearn.metrics import precision_score, recall_score, f1_score

rf = RandomForestClassifier(n_estimators=99,
                            max_depth=9,
                            random_state=2)
rf.fit(X_train, y_train)

accuracy = accuracy_score(y_test, y_pred_test)
precision = precision_score(y_test, y_pred_test, average='macro')
recall = recall_score(y_test, y_pred_test, average='macro')
f1 = f1_score(y_test, y_pred_test, average='macro')

print ("Accuracy: {:0.1f}%, Precision: {:0.1f}%, Recall: {:0.1f}%, F1 Score: {:0.1f}%".format(100*accuracy, 100* precision, 100*recall, 100*f1))


# Finally, are the 4 metrics calculated for random forest better or worst than KNN and Decision Tree models? Which model will you propose for your stakeholder to adopt? Justify your reasons for choosing it?
# 

# Your response here ... 
# 

# You may save your random forest model for future use. 
# 

# In[18]:


import pickle as pk

model_filename = ROOT_DIR + "/model/rf.mdl"
with open(model_filename, "wb") as file:
    pk.dump(rf, file)
print("Model Saved")


# ***
